import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Platform, Text, View } from 'react-native';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';

// 共享的头部组件，确保一致性
const SharedHeaderBackground = ({
  title = '自定义Header',
}: {
  title?: string;
}) => (
  <View
    style={{
      backgroundColor: 'skyblue',
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    <Text
      style={{
        fontSize: 20,
        fontWeight: 'bold',
        color: 'white',
        ...Platform.select({
          ios: {
            color: 'white',
          },
          default: {
            marginTop: 30,
          },
        }),
      }}
    >
      {title}
    </Text>
  </View>
);

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack
        screenOptions={{
          headerShown: true,
          headerTitle: '',
          headerBackground: () => <SharedHeaderBackground />,
          headerStyle: {
            backgroundColor: 'skyblue',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          // 添加动画配置以减少闪烁
          animation: 'slide_from_right',
          animationDuration: 200,
        }}
      >
        <Stack.Screen
          name="(tabs)"
          options={{
            headerShown: false,
            // 确保 tab 页面的过渡动画
            animation: 'fade',
          }}
        />
        <Stack.Screen
          name="day1"
          options={{
            headerBackground: () => <SharedHeaderBackground title="实现时钟" />,
            headerBackTitle: 'Back',
          }}
        />
        <Stack.Screen
          name="day2"
          options={{
            headerBackground: () => <SharedHeaderBackground title="Day 2" />,
            headerBackTitle: 'Back',
          }}
        />
        <Stack.Screen
          name="day3"
          options={{
            headerBackground: () => <SharedHeaderBackground title="Day 3" />,
            headerBackTitle: 'Back',
          }}
        />
        <Stack.Screen
          name="day4"
          options={{
            headerBackground: () => <SharedHeaderBackground title="Day 4" />,
            headerBackTitle: 'Back',
          }}
        />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}

export { SharedHeaderBackground };
