import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { View } from 'react-native';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';

// 共享的头部背景组件，确保一致性
const SharedHeaderBackground = () => (
  <View
    style={{
      backgroundColor: 'skyblue',
      flex: 1,
    }}
  />
);

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack
        screenOptions={{
          // 默认不显示 header，只在需要的页面显示
          headerShown: false,
          // 添加动画配置以减少闪烁
          animation: 'slide_from_right',
          animationDuration: 200,
        }}
      >
        <Stack.Screen
          name="(tabs)"
          options={{
            headerShown: false,
            // 确保 tab 页面的过渡动画
            animation: 'fade',
          }}
        />
        <Stack.Screen
          name="day1"
          options={{
            headerShown: true,
            title: '实现时钟',
            headerBackground: () => <SharedHeaderBackground />,
            headerBackTitle: 'Back',
            headerStyle: {
              backgroundColor: 'skyblue',
            },
            headerTintColor: '#fff',
            headerTitleStyle: {
              fontWeight: 'bold',
              fontSize: 20,
            },
          }}
        />
        <Stack.Screen
          name="day2"
          options={{
            headerShown: true,
            title: 'Day 2 - Components',
            headerBackground: () => <SharedHeaderBackground />,
            headerBackTitle: 'Back',
            headerStyle: {
              backgroundColor: 'skyblue',
            },
            headerTintColor: '#fff',
            headerTitleStyle: {
              fontWeight: 'bold',
              fontSize: 20,
            },
          }}
        />
        <Stack.Screen
          name="day3"
          options={{
            headerShown: true,
            title: 'Day 3 - State',
            headerBackground: () => <SharedHeaderBackground />,
            headerBackTitle: 'Back',
            headerStyle: {
              backgroundColor: 'skyblue',
            },
            headerTintColor: '#fff',
            headerTitleStyle: {
              fontWeight: 'bold',
              fontSize: 20,
            },
          }}
        />
        <Stack.Screen
          name="day4"
          options={{
            headerShown: true,
            title: 'Day 4 - Events',
            headerBackground: () => <SharedHeaderBackground />,
            headerBackTitle: 'Back',
            headerStyle: {
              backgroundColor: 'skyblue',
            },
            headerTintColor: '#fff',
            headerTitleStyle: {
              fontWeight: 'bold',
              fontSize: 20,
            },
          }}
        />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}

export { SharedHeaderBackground };
