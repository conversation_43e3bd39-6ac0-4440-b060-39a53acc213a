{"expo": {"name": "myExpoApp", "slug": "myExpoApp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myexpoapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.anonymous.myExpoApp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "e5bf0028-95b4-41b5-a5e2-d3d1c7937846"}}}}