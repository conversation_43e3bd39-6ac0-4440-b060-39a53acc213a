import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { StyleSheet } from 'react-native';

export default function Day1Screen() {
  // 移除 useEffect 和 navigation.setOptions，因为头部配置已经在 _layout.tsx 中统一处理
  return (
    <ThemedView style={styles.container}>
      <ThemedText type="title">Day 1: Introduction to React Native</ThemedText>
      <ThemedText style={styles.content}>
        Welcome to React Native! This is your first day learning about building
        mobile apps with React Native.
      </ThemedText>
      <ThemedText style={styles.content}>
        React Native lets you build mobile apps using only JavaScript. It uses
        the same design as React, allowing you to compose a rich mobile UI from
        declarative components.
      </ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    marginTop: 20,
    textAlign: 'center',
    lineHeight: 24,
  },
});
