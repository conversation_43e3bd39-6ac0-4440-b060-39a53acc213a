import { ThemedText } from '@/components/ThemedText';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function Day1Screen() {
  // 状态定义: 0-未开始, 1-进行中, 2-已暂停
  const [status, setStatus] = useState(0);
  const [time, setTime] = useState<number>(0);
  const [list, setList] = useState<{ time: number }[]>([]);

  const scrollViewRef = useRef<ScrollView>(null);
  const timer = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0); // 开始时间
  const lastCountTimeRef = useRef<number>(0); // 上次计数时间
  const pausedTimeRef = useRef<number>(0); // 暂停累计时间

  // 计算当前间隔时间
  const currentInterval = useMemo(() => {
    if (list.length === 0) {
      return time;
    } else {
      const totalPreviousTime = list.reduce((sum, item) => sum + item.time, 0);
      return time - totalPreviousTime;
    }
  }, [time, list]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timer.current) {
        clearInterval(timer.current);
      }
    };
  }, []);
  const handleCount = () => {
    if (status === 2) {
      // 复位操作
      setTime(0);
      setStatus(0);
      setList([]);
      startTimeRef.current = 0;
      lastCountTimeRef.current = 0;
      pausedTimeRef.current = 0;
      timer.current && clearInterval(timer.current);
      timer.current = null;
    } else if (status === 1) {
      // 计数操作：记录当前间隔时间
      const currentTime = time;
      const intervalTime =
        list.length === 0
          ? currentTime
          : currentTime - list.reduce((sum, item) => sum + item.time, 0);

      setList((prevList) => [
        ...prevList,
        { time: Number(intervalTime.toFixed(2)) },
      ]);

      // 滚动到底部
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  const start = () => {
    if (status === 0) {
      // 首次启动
      startTimeRef.current = Date.now();
      lastCountTimeRef.current = 0;
    } else if (status === 2) {
      // 从暂停恢复
      startTimeRef.current = Date.now() - time * 1000;
    }

    timer.current = setInterval(() => {
      const elapsed = (Date.now() - startTimeRef.current) / 1000;
      setTime(Number(elapsed.toFixed(2)));
    }, 10); // 更高精度的更新频率

    setStatus(1);
  };

  const handleStart = () => {
    if (status === 1) {
      setStatus(2);
      timer.current && clearInterval(timer.current);
      return;
    }
    if (status === 0 || status === 2) {
      start();
      return;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <ThemedText type="title" style={{ fontSize: 66, fontWeight: 200 }}>
          {time.toFixed(2)}
        </ThemedText>
        <Text style={{ fontSize: 20, color: '#666', marginTop: 10 }}>
          间隔: {currentInterval.toFixed(2)}s
        </Text>
      </View>

      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-around',
          marginVertical: 20,
        }}
      >
        <TouchableOpacity
          onPress={() => handleCount()}
          style={[styles.btn, status === 2 && styles.resetBtn]}
        >
          <Text style={styles.btnText}>{status === 2 ? '复位' : '计数'}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleStart()}
          style={[
            styles.btn,
            (status === 0 || status === 2) && styles.startBtn,
            status === 1 && styles.pauseBtn,
          ]}
        >
          <Text
            style={[
              styles.btnText,
              (status === 1 || status === 0 || status === 2) && {
                color: '#fff',
              },
            ]}
          >
            {status === 0 || status === 2 ? '启动' : '暂停'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView ref={scrollViewRef} style={styles.list}>
        {list.map((item, idx) => (
          <View style={styles.listItem} key={idx}>
            <Text style={{ fontSize: 20, color: '#ccc' }}>计次{idx + 1}</Text>
            <Text style={{ fontSize: 24 }}>{item.time.toFixed(2)}</Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f5f5f5',
    height: '100%',
  },

  content: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    height: '30%',
    width: '100%',
  },

  btn: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    width: 80,
    height: 80,
    borderRadius: 50,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },

  btnText: {
    fontSize: 16,
    fontWeight: '600',
  },

  resetBtn: {
    backgroundColor: '#ff6b6b',
  },

  pauseBtn: {
    backgroundColor: '#ff6b6b',
  },

  startBtn: {
    backgroundColor: '#4ecdc4',
  },

  list: {
    // alignItems: 'center',
  },

  listItem: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderBottomWidth: 1,
    paddingVertical: 10,
    borderBottomColor: '#ccc',
  },
});
