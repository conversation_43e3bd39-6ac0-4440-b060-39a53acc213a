import React, { useEffect, useRef } from 'react';
import { Animated, View, StyleSheet } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

interface SmoothTransitionProps {
  children: React.ReactNode;
}

export default function SmoothTransition({ children }: SmoothTransitionProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useFocusEffect(
    React.useCallback(() => {
      // 页面进入时的动画
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();

      return () => {
        // 页面离开时保持可见，避免空白
        fadeAnim.setValue(1);
        scaleAnim.setValue(1);
      };
    }, [fadeAnim, scaleAnim])
  );

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
